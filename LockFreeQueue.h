#pragma once

#include <atomic>
#include <memory>

template<typename T>
class LockFreeQueue {
private:
    struct Node {
        std::atomic<T*> data{nullptr};
        std::atomic<Node*> next{nullptr};
    };
    
    std::atomic<Node*> head{new Node};
    std::atomic<Node*> tail{head.load()};

public:
    LockFreeQueue() = default;
    
    ~LockFreeQueue() {
        while (Node* const old_head = head.load()) {
            head.store(old_head->next);
            delete old_head;
        }
    }
    
    // 禁用拷贝和赋值
    LockFreeQueue(const LockFreeQueue&) = delete;
    LockFreeQueue& operator=(const LockFreeQueue&) = delete;
    
    void push(T item) {
        Node* const new_node = new Node;
        T* const data = new T(std::move(item));
        new_node->data.store(data);
        
        Node* prev_tail = tail.exchange(new_node);
        prev_tail->next.store(new_node);
    }
    
    bool try_pop(T& result) {
        Node* head_node = head.load();
        Node* const next = head_node->next.load();
        
        if (next == nullptr) {
            return false;
        }
        
        T* const data = next->data.exchange(nullptr);
        if (data == nullptr) {
            return false;
        }
        
        result = *data;
        delete data;
        
        // 尝试更新head指针
        head.compare_exchange_weak(head_node, next);
        delete head_node;
        
        return true;
    }
    
    bool empty() const {
        Node* head_node = head.load();
        Node* const next = head_node->next.load();
        return next == nullptr;
    }
};

// 使用无锁队列的线程池
#include <vector>
#include <thread>
#include <condition_variable>
#include <future>
#include <functional>

class LockFreeThreadPool {
public:
    explicit LockFreeThreadPool(size_t threads);
    ~LockFreeThreadPool();
    
    template<class F, class... Args>
    auto submit(F&& f, Args&&... args) 
        -> std::future<typename std::result_of<F(Args...)>::type>;
    
    size_t size() const { return workers.size(); }
    bool is_stopped() const { return stop.load(); }

private:
    std::vector<std::thread> workers;
    LockFreeQueue<std::function<void()>> tasks;
    std::atomic<bool> stop{false};
    
    // 仍需要条件变量来唤醒等待的线程
    std::condition_variable condition;
    std::mutex cv_mutex;
    
    void worker_thread();
};

template<class F, class... Args>
auto LockFreeThreadPool::submit(F&& f, Args&&... args) 
    -> std::future<typename std::result_of<F(Args...)>::type> {
    
    using return_type = typename std::result_of<F(Args...)>::type;
    
    auto task = std::make_shared<std::packaged_task<return_type()>>(
        std::bind(std::forward<F>(f), std::forward<Args>(args)...)
    );
    
    std::future<return_type> res = task->get_future();
    
    if (stop.load()) {
        throw std::runtime_error("submit on stopped LockFreeThreadPool");
    }
    
    tasks.push([task](){ (*task)(); });
    condition.notify_one();
    
    return res;
}
