#pragma once

#include <vector>
#include <queue>
#include <memory>
#include <thread>
#include <mutex>
#include <condition_variable>
#include <future>
#include <functional>
#include <atomic>

enum class TaskPriority : int
{
    LOW = 0,
    NORMAL = 1,
    HIGH = 2,
    CRITICAL = 3
};

class PriorityThreadPool
{
public:
    explicit PriorityThreadPool(size_t threads);
    ~PriorityThreadPool();

    // 提交带优先级的任务
    template <class F, class... Args>
    auto submit(TaskPriority priority, F &&f, Args &&...args)
        -> std::future<typename std::result_of<F(Args...)>::type>;

    // 提交普通任务（默认优先级）
    template <class F, class... Args>
    auto submit(F &&f, Args &&...args)
        -> std::future<typename std::result_of<F(Args...)>::type>
    {
        return submit(TaskPriority::NORMAL, std::forward<F>(f), std::forward<Args>(args)...);
    }

    size_t size() const { return workers.size(); }
    bool is_stopped() const { return stop.load(); }

    // 获取各优先级队列的任务数量
    struct QueueStats
    {
        size_t critical_tasks = 0;
        size_t high_tasks = 0;
        size_t normal_tasks = 0;
        size_t low_tasks = 0;
        size_t total_tasks = 0;
    };
    QueueStats get_queue_stats() const;

private:
    struct Task
    {
        std::function<void()> function;
        TaskPriority priority;
        uint64_t sequence_number; // 用于同优先级任务的FIFO排序

        Task(std::function<void()> f, TaskPriority p, uint64_t seq)
            : function(std::move(f)), priority(p), sequence_number(seq) {}

        // 优先级比较：优先级高的先执行，同优先级按提交顺序
        bool operator<(const Task &other) const
        {
            if (priority != other.priority)
            {
                return static_cast<int>(priority) < static_cast<int>(other.priority);
            }
            return sequence_number > other.sequence_number; // 注意：priority_queue是大顶堆
        }
    };

    std::vector<std::thread> workers;
    std::priority_queue<Task> tasks;
    mutable std::mutex queue_mutex;
    std::condition_variable condition;
    std::atomic<bool> stop{false};
    std::atomic<uint64_t> sequence_counter{0};

    void worker_thread();
};

// 实现部分
inline PriorityThreadPool::PriorityThreadPool(size_t threads)
{
    for (size_t i = 0; i < threads; ++i)
    {
        workers.emplace_back(&PriorityThreadPool::worker_thread, this);
    }
}

inline PriorityThreadPool::~PriorityThreadPool()
{
    {
        std::unique_lock<std::mutex> lock(queue_mutex);
        stop = true;
    }
    condition.notify_all();

    for (std::thread &worker : workers)
    {
        worker.join();
    }
}

inline void PriorityThreadPool::worker_thread()
{
    for (;;)
    {
        Task task([] {}, TaskPriority::LOW, 0);

        {
            std::unique_lock<std::mutex> lock(queue_mutex);
            condition.wait(lock, [this]
                           { return stop || !tasks.empty(); });

            if (stop && tasks.empty())
            {
                return;
            }

            task = std::move(const_cast<Task &>(tasks.top()));
            tasks.pop();
        }

        task.function();
    }
}

inline PriorityThreadPool::QueueStats PriorityThreadPool::get_queue_stats() const
{
    std::lock_guard<std::mutex> lock(queue_mutex);

    QueueStats stats;
    auto temp_queue = tasks; // 复制队列来遍历

    while (!temp_queue.empty())
    {
        const auto &task = temp_queue.top();
        switch (task.priority)
        {
        case TaskPriority::CRITICAL:
            stats.critical_tasks++;
            break;
        case TaskPriority::HIGH:
            stats.high_tasks++;
            break;
        case TaskPriority::NORMAL:
            stats.normal_tasks++;
            break;
        case TaskPriority::LOW:
            stats.low_tasks++;
            break;
        }
        temp_queue.pop();
    }

    stats.total_tasks = stats.critical_tasks + stats.high_tasks +
                        stats.normal_tasks + stats.low_tasks;
    return stats;
}

template <class F, class... Args>
auto PriorityThreadPool::submit(TaskPriority priority, F &&f, Args &&...args)
    -> std::future<typename std::result_of<F(Args...)>::type>
{

    using return_type = typename std::result_of<F(Args...)>::type;

    auto task = std::make_shared<std::packaged_task<return_type()>>(
        std::bind(std::forward<F>(f), std::forward<Args>(args)...));

    std::future<return_type> res = task->get_future();

    {
        std::unique_lock<std::mutex> lock(queue_mutex);

        if (stop.load())
        {
            throw std::runtime_error("submit on stopped PriorityThreadPool");
        }

        uint64_t seq = sequence_counter.fetch_add(1);
        tasks.emplace([task]()
                      { (*task)(); }, priority, seq);
    }

    condition.notify_one();
    return res;
}
