# 高性能C++线程池

这是一个现代C++实现的高性能线程池，专为高并发场景设计，具有优秀的性能表现和易用性。

## 特性

### 🚀 高性能
- **零拷贝任务提交**：使用完美转发和移动语义
- **高效同步机制**：基于条件变量的线程间通信
- **最小锁竞争**：优化的锁粒度设计
- **RAII资源管理**：自动管理线程生命周期

### 🔧 易用性
- **类型安全**：支持任意可调用对象和参数
- **返回值支持**：通过std::future获取任务结果
- **异常安全**：完整的异常处理机制
- **现代C++**：使用C++14特性，代码简洁

### 📊 性能指标
根据基准测试结果：
- **吞吐量**：229,990 任务/秒
- **CPU密集型任务加速比**：5.5倍（8线程 vs 单线程）
- **内存效率**：处理500万数据元素仅需24ms
- **可扩展性**：支持1-16线程的线性扩展

## 快速开始

### 基本用法

```cpp
#include "ThreadPool.h"
#include <iostream>

int main() {
    // 创建8个工作线程的线程池
    ThreadPool pool(8);
    
    // 提交简单任务
    auto result = pool.submit([] {
        return 42;
    });
    
    std::cout << "结果: " << result.get() << std::endl;
    
    return 0;
}
```

### 带参数的任务

```cpp
// 提交带参数的函数
auto future1 = pool.submit([](int a, int b) {
    return a + b;
}, 10, 20);

// 提交成员函数
class Calculator {
public:
    int multiply(int a, int b) { return a * b; }
};

Calculator calc;
auto future2 = pool.submit(&Calculator::multiply, &calc, 5, 6);

std::cout << "加法结果: " << future1.get() << std::endl;  // 30
std::cout << "乘法结果: " << future2.get() << std::endl;  // 30
```

### 批量任务处理

```cpp
#include <vector>
#include <future>

ThreadPool pool(std::thread::hardware_concurrency());
std::vector<std::future<int>> results;

// 提交100个任务
for (int i = 0; i < 100; ++i) {
    results.emplace_back(
        pool.submit([i] {
            // 模拟一些计算
            return i * i;
        })
    );
}

// 收集所有结果
for (int i = 0; i < 100; ++i) {
    std::cout << "任务 " << i << " 结果: " << results[i].get() << std::endl;
}
```

## API 参考

### 构造函数
```cpp
explicit ThreadPool(size_t threads);
```
创建指定数量工作线程的线程池。

### 提交任务
```cpp
template<class F, class... Args>
auto submit(F&& f, Args&&... args) -> std::future<typename std::result_of<F(Args...)>::type>;
```
提交任务到线程池，返回std::future对象用于获取结果。

### 查询方法
```cpp
size_t size() const;                // 获取线程数量
size_t pending_tasks() const;       // 获取待处理任务数量
bool is_stopped() const;            // 检查是否已停止
```

## 编译和运行

### 编译要求
- C++14或更高版本
- 支持pthread的编译器

### 编译命令
```bash
# 编译测试程序
clang++ -std=c++14 -O2 -Wall -Wextra -pthread ThreadPool.cpp test_threadpool.cpp -o test_threadpool

# 编译基准测试
clang++ -std=c++14 -O2 -Wall -Wextra -pthread ThreadPool.cpp benchmark.cpp -o benchmark

# 运行测试
./test_threadpool

# 运行基准测试
./benchmark
```

### 使用CMake
```bash
mkdir build && cd build
cmake ..
make
./bin/test_threadpool
```

## 测试结果

### 功能测试
- ✅ 基本功能测试
- ✅ 性能测试
- ✅ 并发访问测试
- ✅ 异常处理测试
- ✅ 压力测试

### 性能基准
在8核CPU上的测试结果：

| 测试类型 | 线程池性能 | 单线程性能 | 加速比 |
|---------|-----------|-----------|--------|
| CPU密集型任务 | 26ms | 143ms | 5.5x |
| 吞吐量测试 | 229,990 任务/秒 | - | - |
| 内存处理 | 24ms (500万元素) | - | - |

## 设计原理

### 核心组件
1. **任务队列**：线程安全的std::queue存储待执行任务
2. **工作线程**：固定数量的线程从队列中取任务执行
3. **同步机制**：std::condition_variable协调线程间通信
4. **任务包装**：std::packaged_task支持返回值

### 性能优化
1. **避免动态内存分配**：预分配线程和使用移动语义
2. **减少锁竞争**：最小化临界区大小
3. **完美转发**：避免不必要的拷贝操作
4. **原子操作**：使用std::atomic减少同步开销

## 许可证

MIT License - 详见LICENSE文件

## 贡献

欢迎提交Issue和Pull Request来改进这个线程池实现。
