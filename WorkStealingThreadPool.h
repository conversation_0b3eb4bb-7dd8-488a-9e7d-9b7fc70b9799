#pragma once

#include <vector>
#include <deque>
#include <memory>
#include <thread>
#include <mutex>
#include <condition_variable>
#include <future>
#include <functional>
#include <atomic>
#include <random>

class WorkStealingThreadPool
{
public:
    explicit WorkStealingThreadPool(size_t threads = std::thread::hardware_concurrency());
    ~WorkStealingThreadPool();

    template <class F, class... Args>
    auto submit(F &&f, Args &&...args)
        -> std::future<typename std::result_of<F(Args...)>::type>;

    size_t size() const { return workers.size(); }
    bool is_stopped() const { return stop.load(); }

    // 获取统计信息
    struct Stats
    {
        size_t total_tasks_executed = 0;
        size_t total_tasks_stolen = 0;
        std::vector<size_t> per_thread_tasks;
        std::vector<size_t> per_thread_steals;
    };
    Stats get_stats() const;

private:
    // 工作线程的本地队列
    struct WorkerData
    {
        std::deque<std::function<void()>> tasks;
        mutable std::mutex mutex;
        std::atomic<size_t> tasks_executed{0};
        std::atomic<size_t> tasks_stolen{0};
    };

    std::vector<std::thread> workers;
    std::vector<std::unique_ptr<WorkerData>> worker_data;
    std::atomic<bool> stop{false};
    std::condition_variable cv;
    std::mutex cv_mutex;

    // 线程本地存储，用于快速访问当前线程的队列
    static thread_local size_t thread_index;

    void worker_thread(size_t index);
    bool try_steal_task(size_t thief_index, std::function<void()> &task);
    void push_task_to_local_queue(size_t thread_idx, std::function<void()> task);
    bool pop_task_from_local_queue(size_t thread_idx, std::function<void()> &task);
};

template <class F, class... Args>
auto WorkStealingThreadPool::submit(F &&f, Args &&...args)
    -> std::future<typename std::result_of<F(Args...)>::type>
{

    using return_type = typename std::result_of<F(Args...)>::type;

    auto task = std::make_shared<std::packaged_task<return_type()>>(
        std::bind(std::forward<F>(f), std::forward<Args>(args)...));

    std::future<return_type> res = task->get_future();

    if (stop.load())
    {
        throw std::runtime_error("submit on stopped WorkStealingThreadPool");
    }

    // 尝试添加到当前线程的本地队列
    size_t current_thread = thread_index;
    if (current_thread != SIZE_MAX && current_thread < worker_data.size())
    {
        push_task_to_local_queue(current_thread, [task]()
                                 { (*task)(); });
    }
    else
    {
        // 如果不在工作线程中，随机选择一个线程的队列
        static thread_local std::random_device rd;
        static thread_local std::mt19937 gen(rd());
        std::uniform_int_distribution<> dis(0, worker_data.size() - 1);
        size_t target_thread = dis(gen);
        push_task_to_local_queue(target_thread, [task]()
                                 { (*task)(); });
    }

    cv.notify_one();
    return res;
}
