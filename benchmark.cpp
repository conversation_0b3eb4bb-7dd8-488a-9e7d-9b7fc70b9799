#include "ThreadPool.h"
#include <iostream>
#include <chrono>
#include <vector>
#include <numeric>
#include <random>
#include <algorithm>

// CPU密集型任务：计算素数
bool is_prime(int n) {
    if (n < 2) return false;
    if (n == 2) return true;
    if (n % 2 == 0) return false;
    
    for (int i = 3; i * i <= n; i += 2) {
        if (n % i == 0) return false;
    }
    return true;
}

// 计算范围内的素数个数
int count_primes(int start, int end) {
    int count = 0;
    for (int i = start; i < end; ++i) {
        if (is_prime(i)) {
            count++;
        }
    }
    return count;
}

// 矩阵乘法任务
std::vector<std::vector<int>> matrix_multiply(
    const std::vector<std::vector<int>>& a,
    const std::vector<std::vector<int>>& b) {
    
    int n = a.size();
    int m = b[0].size();
    int p = b.size();
    
    std::vector<std::vector<int>> result(n, std::vector<int>(m, 0));
    
    for (int i = 0; i < n; ++i) {
        for (int j = 0; j < m; ++j) {
            for (int k = 0; k < p; ++k) {
                result[i][j] += a[i][k] * b[k][j];
            }
        }
    }
    
    return result;
}

void benchmark_cpu_intensive() {
    std::cout << "=== CPU密集型任务基准测试 ===" << std::endl;
    
    const int range_size = 10000;
    const int num_tasks = 100;
    const int num_threads = std::thread::hardware_concurrency();
    
    std::cout << "任务：计算素数个数" << std::endl;
    std::cout << "每个任务范围：" << range_size << " 个数字" << std::endl;
    std::cout << "任务数量：" << num_tasks << std::endl;
    std::cout << "线程数：" << num_threads << std::endl << std::endl;
    
    // 线程池测试
    {
        ThreadPool pool(num_threads);
        auto start = std::chrono::high_resolution_clock::now();
        
        std::vector<std::future<int>> results;
        for (int i = 0; i < num_tasks; ++i) {
            int start_num = i * range_size;
            int end_num = (i + 1) * range_size;
            results.emplace_back(
                pool.submit(count_primes, start_num, end_num)
            );
        }
        
        int total_primes = 0;
        for (auto& result : results) {
            total_primes += result.get();
        }
        
        auto end = std::chrono::high_resolution_clock::now();
        auto duration = std::chrono::duration_cast<std::chrono::milliseconds>(end - start);
        
        std::cout << "线程池执行时间: " << duration.count() << " ms" << std::endl;
        std::cout << "找到的素数总数: " << total_primes << std::endl;
    }
    
    // 单线程测试
    {
        auto start = std::chrono::high_resolution_clock::now();
        
        int total_primes = 0;
        for (int i = 0; i < num_tasks; ++i) {
            int start_num = i * range_size;
            int end_num = (i + 1) * range_size;
            total_primes += count_primes(start_num, end_num);
        }
        
        auto end = std::chrono::high_resolution_clock::now();
        auto duration = std::chrono::duration_cast<std::chrono::milliseconds>(end - start);
        
        std::cout << "单线程执行时间: " << duration.count() << " ms" << std::endl;
        std::cout << "找到的素数总数: " << total_primes << std::endl;
    }
    
    std::cout << std::endl;
}

void benchmark_throughput() {
    std::cout << "=== 吞吐量基准测试 ===" << std::endl;
    
    const int num_tasks = 10000;
    const int num_threads = std::thread::hardware_concurrency();
    
    std::cout << "任务数量：" << num_tasks << std::endl;
    std::cout << "线程数：" << num_threads << std::endl;
    
    ThreadPool pool(num_threads);
    auto start = std::chrono::high_resolution_clock::now();
    
    std::vector<std::future<int>> results;
    for (int i = 0; i < num_tasks; ++i) {
        results.emplace_back(
            pool.submit([i] {
                // 轻量级任务
                return i * 2;
            })
        );
    }
    
    // 等待所有任务完成
    for (auto& result : results) {
        result.get();
    }
    
    auto end = std::chrono::high_resolution_clock::now();
    auto duration = std::chrono::duration_cast<std::chrono::microseconds>(end - start);
    
    double tasks_per_second = (double)num_tasks / duration.count() * 1000000;
    
    std::cout << "总执行时间: " << duration.count() << " 微秒" << std::endl;
    std::cout << "吞吐量: " << (int)tasks_per_second << " 任务/秒" << std::endl;
    std::cout << "平均每个任务: " << (double)duration.count() / num_tasks << " 微秒" << std::endl;
    
    std::cout << std::endl;
}

void benchmark_scalability() {
    std::cout << "=== 可扩展性基准测试 ===" << std::endl;
    
    const int num_tasks = 1000;
    const int max_threads = std::thread::hardware_concurrency() * 2;
    
    std::cout << "任务数量：" << num_tasks << std::endl;
    std::cout << "测试不同线程数的性能..." << std::endl << std::endl;
    
    for (int threads = 1; threads <= max_threads; threads *= 2) {
        ThreadPool pool(threads);
        auto start = std::chrono::high_resolution_clock::now();
        
        std::vector<std::future<int>> results;
        for (int i = 0; i < num_tasks; ++i) {
            results.emplace_back(
                pool.submit(count_primes, i * 100, (i + 1) * 100)
            );
        }
        
        int total = 0;
        for (auto& result : results) {
            total += result.get();
        }
        
        auto end = std::chrono::high_resolution_clock::now();
        auto duration = std::chrono::duration_cast<std::chrono::milliseconds>(end - start);
        
        std::cout << "线程数: " << threads 
                  << ", 执行时间: " << duration.count() << " ms"
                  << ", 结果: " << total << std::endl;
    }
    
    std::cout << std::endl;
}

void benchmark_memory_usage() {
    std::cout << "=== 内存使用基准测试 ===" << std::endl;
    
    const int num_tasks = 5000;
    const int num_threads = std::thread::hardware_concurrency();
    
    std::cout << "测试大量任务的内存使用情况..." << std::endl;
    std::cout << "任务数量：" << num_tasks << std::endl;
    
    ThreadPool pool(num_threads);
    
    // 提交大量任务但不立即获取结果
    std::vector<std::future<std::vector<int>>> results;
    
    auto start = std::chrono::high_resolution_clock::now();
    
    for (int i = 0; i < num_tasks; ++i) {
        results.emplace_back(
            pool.submit([i] {
                // 创建一些数据
                std::vector<int> data(1000);
                std::iota(data.begin(), data.end(), i);
                return data;
            })
        );
    }
    
    std::cout << "所有任务已提交，等待执行完成..." << std::endl;
    std::cout << "当前队列中待处理任务: " << pool.pending_tasks() << std::endl;
    
    // 获取所有结果
    size_t total_elements = 0;
    for (auto& result : results) {
        auto data = result.get();
        total_elements += data.size();
    }
    
    auto end = std::chrono::high_resolution_clock::now();
    auto duration = std::chrono::duration_cast<std::chrono::milliseconds>(end - start);
    
    std::cout << "执行时间: " << duration.count() << " ms" << std::endl;
    std::cout << "处理的数据元素总数: " << total_elements << std::endl;
    std::cout << "最终队列中待处理任务: " << pool.pending_tasks() << std::endl;
    
    std::cout << std::endl;
}

int main() {
    std::cout << "开始线程池性能基准测试..." << std::endl;
    std::cout << "硬件并发线程数: " << std::thread::hardware_concurrency() << std::endl;
    std::cout << std::endl;
    
    benchmark_cpu_intensive();
    benchmark_throughput();
    benchmark_scalability();
    benchmark_memory_usage();
    
    std::cout << "所有基准测试完成！" << std::endl;
    
    return 0;
}
