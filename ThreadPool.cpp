#include "ThreadPool.h"

ThreadPool::ThreadPool(size_t threads) : stop(false)
{
    // 创建指定数量的工作线程
    for (size_t i = 0; i < threads; ++i)
    {
        workers.emplace_back([this]
                             {
            // 工作线程的主循环
            for(;;) {
                std::function<void()> task;
                
                {
                    std::unique_lock<std::mutex> lock(this->queue_mutex);
                    
                    // 等待任务或停止信号
                    this->condition.wait(lock, [this] {
                        return this->stop || !this->tasks.empty();
                    });
                    
                    // 如果收到停止信号且队列为空，退出线程
                    if(this->stop && this->tasks.empty()) {
                        return;
                    }
                    
                    // 从队列中取出一个任务
                    task = std::move(this->tasks.front());
                    this->tasks.pop();
                }
                
                // 执行任务
                task();
            } });
    }
}

ThreadPool::~ThreadPool()
{
    {
        std::unique_lock<std::mutex> lock(queue_mutex);
        stop = true;
    }

    // 通知所有线程停止
    condition.notify_all();

    // 等待所有线程完成
    for (std::thread &worker : workers)
    {
        worker.join();
    }
}

size_t ThreadPool::size() const
{
    return workers.size();
}

size_t ThreadPool::pending_tasks() const
{
    std::lock_guard<std::mutex> lock(queue_mutex);
    return tasks.size();
}

bool ThreadPool::is_stopped() const
{
    return stop.load();
}
