#include "WorkStealingThreadPool.h"
#include <algorithm>

// 线程本地存储定义
thread_local size_t WorkStealingThreadPool::thread_index = SIZE_MAX;

WorkStealingThreadPool::WorkStealingThreadPool(size_t threads)
{
    worker_data.reserve(threads);
    workers.reserve(threads);

    // 创建工作线程数据
    for (size_t i = 0; i < threads; ++i)
    {
        worker_data.emplace_back(std::make_unique<WorkerData>());
    }

    // 启动工作线程
    for (size_t i = 0; i < threads; ++i)
    {
        workers.emplace_back(&WorkStealingThreadPool::worker_thread, this, i);
    }
}

WorkStealingThreadPool::~WorkStealingThreadPool()
{
    stop.store(true);
    cv.notify_all();

    for (auto &worker : workers)
    {
        if (worker.joinable())
        {
            worker.join();
        }
    }
}

void WorkStealingThreadPool::worker_thread(size_t index)
{
    thread_index = index;

    while (!stop.load())
    {
        std::function<void()> task;

        // 首先尝试从本地队列获取任务
        if (pop_task_from_local_queue(index, task))
        {
            task();
            worker_data[index]->tasks_executed.fetch_add(1);
            continue;
        }

        // 本地队列为空，尝试从其他线程窃取任务
        if (try_steal_task(index, task))
        {
            task();
            worker_data[index]->tasks_executed.fetch_add(1);
            worker_data[index]->tasks_stolen.fetch_add(1);
            continue;
        }

        // 没有任务可执行，等待新任务
        std::unique_lock<std::mutex> lock(cv_mutex);
        cv.wait_for(lock, std::chrono::milliseconds(1), [this]
                    { return stop.load() || std::any_of(worker_data.begin(), worker_data.end(),
                                                        [](const auto &wd)
                                                        {
                                                            std::lock_guard<std::mutex> lg(wd->mutex);
                                                            return !wd->tasks.empty();
                                                        }); });
    }
}

bool WorkStealingThreadPool::try_steal_task(size_t thief_index, std::function<void()> &task)
{
    // 随机选择窃取目标，避免总是从同一个线程窃取
    static thread_local std::random_device rd;
    static thread_local std::mt19937 gen(rd());

    std::vector<size_t> targets;
    for (size_t i = 0; i < worker_data.size(); ++i)
    {
        if (i != thief_index)
        {
            targets.push_back(i);
        }
    }

    std::shuffle(targets.begin(), targets.end(), gen);

    for (size_t target : targets)
    {
        std::lock_guard<std::mutex> lock(worker_data[target]->mutex);
        if (!worker_data[target]->tasks.empty())
        {
            // 从队列头部窃取任务（FIFO for stealing）
            task = std::move(worker_data[target]->tasks.front());
            worker_data[target]->tasks.pop_front();
            return true;
        }
    }

    return false;
}

void WorkStealingThreadPool::push_task_to_local_queue(size_t thread_idx, std::function<void()> task)
{
    std::lock_guard<std::mutex> lock(worker_data[thread_idx]->mutex);
    // 添加到队列尾部（LIFO for local access）
    worker_data[thread_idx]->tasks.push_back(std::move(task));
}

bool WorkStealingThreadPool::pop_task_from_local_queue(size_t thread_idx, std::function<void()> &task)
{
    std::lock_guard<std::mutex> lock(worker_data[thread_idx]->mutex);
    if (worker_data[thread_idx]->tasks.empty())
    {
        return false;
    }

    // 从队列尾部取任务（LIFO for local access，更好的缓存局部性）
    task = std::move(worker_data[thread_idx]->tasks.back());
    worker_data[thread_idx]->tasks.pop_back();
    return true;
}

WorkStealingThreadPool::Stats WorkStealingThreadPool::get_stats() const
{
    Stats stats;
    stats.per_thread_tasks.reserve(worker_data.size());
    stats.per_thread_steals.reserve(worker_data.size());

    for (const auto &wd : worker_data)
    {
        size_t executed = wd->tasks_executed.load();
        size_t stolen = wd->tasks_stolen.load();

        stats.total_tasks_executed += executed;
        stats.total_tasks_stolen += stolen;
        stats.per_thread_tasks.push_back(executed);
        stats.per_thread_steals.push_back(stolen);
    }

    return stats;
}
