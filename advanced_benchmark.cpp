#include "ThreadPool.h"
#include "WorkStealingThreadPool.h"
#include "PriorityThreadPool.h"
#include "LockFreeQueue.h"
#include <iostream>
#include <chrono>
#include <vector>
#include <random>
#include <algorithm>

// 测试任务类型
void cpu_intensive_task(int iterations = 1000)
{
    volatile int sum = 0;
    for (int i = 0; i < iterations; ++i)
    {
        sum += i * i;
    }
}

void io_simulation_task(int ms = 1)
{
    std::this_thread::sleep_for(std::chrono::milliseconds(ms));
}

// 性能测试框架
template <typename ThreadPoolType>
class PerformanceTester
{
public:
    struct TestResult
    {
        std::string test_name;
        std::chrono::milliseconds duration;
        size_t tasks_completed;
        double tasks_per_second;
        double average_task_time_us;
    };

    static TestResult run_cpu_intensive_test(ThreadPoolType &pool,
                                             const std::string &pool_name,
                                             int num_tasks = 1000)
    {
        auto start = std::chrono::high_resolution_clock::now();

        std::vector<std::future<void>> futures;
        futures.reserve(num_tasks);

        for (int i = 0; i < num_tasks; ++i)
        {
            futures.emplace_back(pool.submit(cpu_intensive_task, 500));
        }

        // 等待所有任务完成
        for (auto &future : futures)
        {
            future.get();
        }

        auto end = std::chrono::high_resolution_clock::now();
        auto duration = std::chrono::duration_cast<std::chrono::milliseconds>(end - start);

        TestResult result;
        result.test_name = pool_name + " - CPU Intensive";
        result.duration = duration;
        result.tasks_completed = num_tasks;
        result.tasks_per_second = (double)num_tasks / duration.count() * 1000.0;
        result.average_task_time_us = (double)duration.count() * 1000.0 / num_tasks;

        return result;
    }

    static TestResult run_mixed_workload_test(ThreadPoolType &pool,
                                              const std::string &pool_name,
                                              int num_tasks = 1000)
    {
        auto start = std::chrono::high_resolution_clock::now();

        std::vector<std::future<void>> futures;
        futures.reserve(num_tasks);

        std::random_device rd;
        std::mt19937 gen(rd());
        std::uniform_int_distribution<> task_type_dis(0, 2);

        for (int i = 0; i < num_tasks; ++i)
        {
            int task_type = task_type_dis(gen);

            switch (task_type)
            {
            case 0:
                futures.emplace_back(pool.submit(cpu_intensive_task, 100));
                break;
            case 1:
                futures.emplace_back(pool.submit(io_simulation_task, 1));
                break;
            case 2:
                futures.emplace_back(pool.submit([]()
                                                 {
                        // 轻量级任务
                        volatile int x = 42;
                        x *= 2; }));
                break;
            }
        }

        for (auto &future : futures)
        {
            future.get();
        }

        auto end = std::chrono::high_resolution_clock::now();
        auto duration = std::chrono::duration_cast<std::chrono::milliseconds>(end - start);

        TestResult result;
        result.test_name = pool_name + " - Mixed Workload";
        result.duration = duration;
        result.tasks_completed = num_tasks;
        result.tasks_per_second = (double)num_tasks / duration.count() * 1000.0;
        result.average_task_time_us = (double)duration.count() * 1000.0 / num_tasks;

        return result;
    }

    static TestResult run_high_contention_test(ThreadPoolType &pool,
                                               const std::string &pool_name,
                                               int num_tasks = 5000)
    {
        auto start = std::chrono::high_resolution_clock::now();

        std::vector<std::future<int>> futures;
        futures.reserve(num_tasks);

        // 大量小任务，测试任务调度开销
        for (int i = 0; i < num_tasks; ++i)
        {
            futures.emplace_back(pool.submit([i]() -> int
                                             { return i * 2; }));
        }

        long long sum = 0;
        for (auto &future : futures)
        {
            sum += future.get();
        }
        (void)sum; // 避免未使用变量警告

        auto end = std::chrono::high_resolution_clock::now();
        auto duration = std::chrono::duration_cast<std::chrono::milliseconds>(end - start);

        TestResult result;
        result.test_name = pool_name + " - High Contention";
        result.duration = duration;
        result.tasks_completed = num_tasks;
        result.tasks_per_second = (double)num_tasks / duration.count() * 1000.0;
        result.average_task_time_us = (double)duration.count() * 1000.0 / num_tasks;

        return result;
    }

    static void print_result(const TestResult &result)
    {
        std::cout << "=== " << result.test_name << " ===" << std::endl;
        std::cout << "执行时间: " << result.duration.count() << " ms" << std::endl;
        std::cout << "完成任务: " << result.tasks_completed << std::endl;
        std::cout << "吞吐量: " << (int)result.tasks_per_second << " 任务/秒" << std::endl;
        std::cout << "平均任务时间: " << result.average_task_time_us << " 微秒" << std::endl;
        std::cout << std::endl;
    }
};

void compare_thread_pools()
{
    const size_t num_threads = std::thread::hardware_concurrency();
    const int num_tasks = 1000;

    std::cout << "线程池性能对比测试" << std::endl;
    std::cout << "硬件线程数: " << num_threads << std::endl;
    std::cout << "测试任务数: " << num_tasks << std::endl;
    std::cout << std::string(50, '=') << std::endl;

    // 测试原始线程池
    {
        ThreadPool original_pool(num_threads);
        auto result1 = PerformanceTester<ThreadPool>::run_cpu_intensive_test(
            original_pool, "Original ThreadPool", num_tasks);
        PerformanceTester<ThreadPool>::print_result(result1);

        auto result2 = PerformanceTester<ThreadPool>::run_mixed_workload_test(
            original_pool, "Original ThreadPool", num_tasks);
        PerformanceTester<ThreadPool>::print_result(result2);

        auto result3 = PerformanceTester<ThreadPool>::run_high_contention_test(
            original_pool, "Original ThreadPool", num_tasks * 2);
        PerformanceTester<ThreadPool>::print_result(result3);
    }

    // 测试工作窃取线程池
    {
        WorkStealingThreadPool ws_pool(num_threads);
        auto result1 = PerformanceTester<WorkStealingThreadPool>::run_cpu_intensive_test(
            ws_pool, "Work-Stealing ThreadPool", num_tasks);
        PerformanceTester<WorkStealingThreadPool>::print_result(result1);

        auto result2 = PerformanceTester<WorkStealingThreadPool>::run_mixed_workload_test(
            ws_pool, "Work-Stealing ThreadPool", num_tasks);
        PerformanceTester<WorkStealingThreadPool>::print_result(result2);

        auto result3 = PerformanceTester<WorkStealingThreadPool>::run_high_contention_test(
            ws_pool, "Work-Stealing ThreadPool", num_tasks * 2);
        PerformanceTester<WorkStealingThreadPool>::print_result(result3);

        // 显示工作窃取统计
        auto stats = ws_pool.get_stats();
        std::cout << "工作窃取统计:" << std::endl;
        std::cout << "总执行任务: " << stats.total_tasks_executed << std::endl;
        std::cout << "总窃取任务: " << stats.total_tasks_stolen << std::endl;
        std::cout << "窃取率: " << (double)stats.total_tasks_stolen / stats.total_tasks_executed * 100 << "%" << std::endl;
        std::cout << std::endl;
    }
}

void test_priority_scheduling()
{
    std::cout << "优先级调度测试" << std::endl;
    std::cout << std::string(30, '=') << std::endl;

    PriorityThreadPool priority_pool(4);

    // 提交不同优先级的任务
    std::vector<std::future<std::string>> results;

    // 低优先级任务
    for (int i = 0; i < 5; ++i)
    {
        results.emplace_back(priority_pool.submit(TaskPriority::LOW, [i]() -> std::string
                                                  {
            std::this_thread::sleep_for(std::chrono::milliseconds(10));
            return "LOW-" + std::to_string(i); }));
    }

    // 高优先级任务
    for (int i = 0; i < 3; ++i)
    {
        results.emplace_back(priority_pool.submit(TaskPriority::HIGH, [i]() -> std::string
                                                  {
            std::this_thread::sleep_for(std::chrono::milliseconds(10));
            return "HIGH-" + std::to_string(i); }));
    }

    // 关键优先级任务
    results.emplace_back(priority_pool.submit(TaskPriority::CRITICAL, []() -> std::string
                                              {
        std::this_thread::sleep_for(std::chrono::milliseconds(10));
        return "CRITICAL-0"; }));

    // 收集结果
    std::cout << "任务完成顺序（应该看到高优先级任务先完成）:" << std::endl;
    for (auto &result : results)
    {
        std::cout << result.get() << " ";
    }
    std::cout << std::endl
              << std::endl;
}

int main()
{
    try
    {
        compare_thread_pools();
        test_priority_scheduling();

        std::cout << "所有高级功能测试完成！" << std::endl;
    }
    catch (const std::exception &e)
    {
        std::cerr << "测试失败: " << e.what() << std::endl;
        return 1;
    }

    return 0;
}
