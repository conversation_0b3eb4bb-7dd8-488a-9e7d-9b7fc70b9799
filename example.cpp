#include "ThreadPool.h"
#include <iostream>
#include <vector>
#include <chrono>
#include <random>
#include <queue>
#include <numeric>
#include <cmath>

// 示例1：基本使用
void basic_example()
{
    std::cout << "=== 基本使用示例 ===" << std::endl;

    ThreadPool pool(4);

    // 提交简单任务
    auto future1 = pool.submit([]
                               {
        std::this_thread::sleep_for(std::chrono::milliseconds(100));
        return 42; });

    // 提交带参数的任务
    auto future2 = pool.submit([](int a, int b)
                               { return a * b; }, 6, 7);

    // 提交lambda包装的函数
    auto future3 = pool.submit([](int x)
                               { return std::abs(x); }, -123);

    std::cout << "任务1结果: " << future1.get() << std::endl;
    std::cout << "任务2结果: " << future2.get() << std::endl;
    std::cout << "任务3结果: " << future3.get() << std::endl;
    std::cout << std::endl;
}

// 示例2：并行计算
void parallel_computation_example()
{
    std::cout << "=== 并行计算示例 ===" << std::endl;

    ThreadPool pool(std::thread::hardware_concurrency());

    // 计算大数组的和
    const int array_size = 1000000;
    const int num_chunks = 8;
    const int chunk_size = array_size / num_chunks;

    // 创建测试数据
    std::vector<int> data(array_size);
    std::iota(data.begin(), data.end(), 1);

    auto start = std::chrono::high_resolution_clock::now();

    // 并行计算每个块的和
    std::vector<std::future<long long>> futures;
    for (int i = 0; i < num_chunks; ++i)
    {
        int start_idx = i * chunk_size;
        int end_idx = (i == num_chunks - 1) ? array_size : (i + 1) * chunk_size;

        futures.emplace_back(
            pool.submit([&data, start_idx, end_idx]
                        {
                long long sum = 0;
                for (int j = start_idx; j < end_idx; ++j) {
                    sum += data[j];
                }
                return sum; }));
    }

    // 收集结果
    long long total_sum = 0;
    for (auto &future : futures)
    {
        total_sum += future.get();
    }

    auto end = std::chrono::high_resolution_clock::now();
    auto duration = std::chrono::duration_cast<std::chrono::milliseconds>(end - start);

    std::cout << "数组大小: " << array_size << std::endl;
    std::cout << "并行块数: " << num_chunks << std::endl;
    std::cout << "总和: " << total_sum << std::endl;
    std::cout << "计算时间: " << duration.count() << " ms" << std::endl;
    std::cout << std::endl;
}

// 示例3：生产者-消费者模式
void producer_consumer_example()
{
    std::cout << "=== 生产者-消费者示例 ===" << std::endl;

    ThreadPool pool(6); // 2个生产者 + 4个消费者

    // 共享数据结构
    std::queue<int> shared_queue;
    std::mutex queue_mutex;
    std::atomic<bool> finished(false);
    std::atomic<int> total_processed(0);

    // 生产者任务
    auto producer = [&](int producer_id, int items_to_produce)
    {
        std::random_device rd;
        std::mt19937 gen(rd());
        std::uniform_int_distribution<> dis(1, 100);

        for (int i = 0; i < items_to_produce; ++i)
        {
            int item = dis(gen);

            {
                std::lock_guard<std::mutex> lock(queue_mutex);
                shared_queue.push(item);
            }

            std::cout << "生产者 " << producer_id << " 生产了: " << item << std::endl;
            std::this_thread::sleep_for(std::chrono::milliseconds(10));
        }
    };

    // 消费者任务
    auto consumer = [&](int consumer_id)
    {
        while (!finished.load() || !shared_queue.empty())
        {
            int item = -1;
            bool has_item = false;

            {
                std::lock_guard<std::mutex> lock(queue_mutex);
                if (!shared_queue.empty())
                {
                    item = shared_queue.front();
                    shared_queue.pop();
                    has_item = true;
                }
            }

            if (has_item)
            {
                // 模拟处理时间
                std::this_thread::sleep_for(std::chrono::milliseconds(20));
                total_processed.fetch_add(1);
                std::cout << "消费者 " << consumer_id << " 处理了: " << item << std::endl;
            }
            else
            {
                std::this_thread::sleep_for(std::chrono::milliseconds(5));
            }
        }
    };

    // 启动生产者
    auto producer1 = pool.submit(producer, 1, 10);
    auto producer2 = pool.submit(producer, 2, 10);

    // 启动消费者
    std::vector<std::future<void>> consumers;
    for (int i = 1; i <= 4; ++i)
    {
        consumers.emplace_back(pool.submit(consumer, i));
    }

    // 等待生产者完成
    producer1.get();
    producer2.get();

    // 等待一段时间让消费者处理完剩余项目
    std::this_thread::sleep_for(std::chrono::milliseconds(500));
    finished.store(true);

    // 等待消费者完成
    for (auto &consumer_future : consumers)
    {
        consumer_future.get();
    }

    std::cout << "总共处理的项目数: " << total_processed.load() << std::endl;
    std::cout << std::endl;
}

// 示例4：异常处理
void exception_handling_example()
{
    std::cout << "=== 异常处理示例 ===" << std::endl;

    ThreadPool pool(2);

    // 提交会抛出异常的任务
    auto future1 = pool.submit([]
                               {
        throw std::runtime_error("这是一个测试异常");
        return 100; });

    // 提交正常任务
    auto future2 = pool.submit([]
                               { return 200; });

    // 处理异常
    try
    {
        int result1 = future1.get();
        std::cout << "任务1结果: " << result1 << std::endl;
    }
    catch (const std::exception &e)
    {
        std::cout << "捕获到异常: " << e.what() << std::endl;
    }

    // 正常任务应该不受影响
    try
    {
        int result2 = future2.get();
        std::cout << "任务2结果: " << result2 << std::endl;
    }
    catch (const std::exception &e)
    {
        std::cout << "意外的异常: " << e.what() << std::endl;
    }

    std::cout << std::endl;
}

int main()
{
    std::cout << "C++ 线程池使用示例" << std::endl;
    std::cout << "硬件并发线程数: " << std::thread::hardware_concurrency() << std::endl;
    std::cout << std::endl;

    basic_example();
    parallel_computation_example();
    producer_consumer_example();
    exception_handling_example();

    std::cout << "所有示例运行完成！" << std::endl;

    return 0;
}
