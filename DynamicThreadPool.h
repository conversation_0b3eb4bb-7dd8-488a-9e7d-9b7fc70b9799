#pragma once

#include <vector>
#include <queue>
#include <memory>
#include <thread>
#include <mutex>
#include <condition_variable>
#include <future>
#include <functional>
#include <atomic>
#include <chrono>

class DynamicThreadPool {
public:
    struct Config {
        size_t min_threads = 2;
        size_t max_threads = std::thread::hardware_concurrency() * 2;
        std::chrono::milliseconds idle_timeout{5000};  // 空闲线程超时时间
        size_t scale_up_threshold = 10;    // 队列长度超过此值时增加线程
        size_t scale_down_threshold = 2;   // 队列长度低于此值时考虑减少线程
        std::chrono::milliseconds monitor_interval{1000};  // 监控间隔
    };
    
    explicit DynamicThreadPool(const Config& config = Config{});
    ~DynamicThreadPool();
    
    template<class F, class... Args>
    auto submit(F&& f, Args&&... args) 
        -> std::future<typename std::result_of<F(Args...)>::type>;
    
    size_t size() const { return active_threads.load(); }
    size_t pending_tasks() const;
    bool is_stopped() const { return stop.load(); }
    
    // 获取线程池状态
    struct Status {
        size_t active_threads;
        size_t pending_tasks;
        size_t total_tasks_executed;
        double average_task_time_ms;
        std::chrono::steady_clock::time_point last_scale_event;
    };
    Status get_status() const;
    
    // 手动调整线程数
    void set_thread_count(size_t count);

private:
    Config config_;
    std::vector<std::thread> workers;
    std::queue<std::function<void()>> tasks;
    mutable std::mutex queue_mutex;
    std::condition_variable condition;
    std::condition_variable worker_condition;
    
    std::atomic<bool> stop{false};
    std::atomic<size_t> active_threads{0};
    std::atomic<size_t> idle_threads{0};
    std::atomic<size_t> total_tasks_executed{0};
    std::atomic<uint64_t> total_task_time_ns{0};
    
    std::thread monitor_thread;
    mutable std::mutex status_mutex;
    std::chrono::steady_clock::time_point last_scale_event;
    
    void worker_thread();
    void monitor_thread_func();
    void scale_up();
    void scale_down();
    bool should_scale_up() const;
    bool should_scale_down() const;
};

template<class F, class... Args>
auto DynamicThreadPool::submit(F&& f, Args&&... args) 
    -> std::future<typename std::result_of<F(Args...)>::type> {
    
    using return_type = typename std::result_of<F(Args...)>::type;
    
    auto task = std::make_shared<std::packaged_task<return_type()>>(
        std::bind(std::forward<F>(f), std::forward<Args>(args)...)
    );
    
    std::future<return_type> res = task->get_future();
    
    {
        std::unique_lock<std::mutex> lock(queue_mutex);
        
        if (stop.load()) {
            throw std::runtime_error("submit on stopped DynamicThreadPool");
        }
        
        // 包装任务以收集执行时间统计
        tasks.emplace([this, task]() {
            auto start = std::chrono::high_resolution_clock::now();
            (*task)();
            auto end = std::chrono::high_resolution_clock::now();
            
            auto duration = std::chrono::duration_cast<std::chrono::nanoseconds>(end - start);
            total_task_time_ns.fetch_add(duration.count());
            total_tasks_executed.fetch_add(1);
        });
    }
    
    condition.notify_one();
    return res;
}
