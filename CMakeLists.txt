cmake_minimum_required(VERSION 3.10)
project(ThreadPoolDemo)

# 设置C++标准
set(CMAKE_CXX_STANDARD 14)
set(CMAKE_CXX_STANDARD_REQUIRED ON)

# 设置编译选项
set(CMAKE_CXX_FLAGS "${CMAKE_CXX_FLAGS} -Wall -Wextra -O2")

# 查找线程库
find_package(Threads REQUIRED)

# 创建线程池库
add_library(threadpool ThreadPool.cpp)
target_link_libraries(threadpool Threads::Threads)

# 创建测试可执行文件
add_executable(test_threadpool test_threadpool.cpp)
target_link_libraries(test_threadpool threadpool Threads::Threads)

# 设置输出目录
set_target_properties(test_threadpool PROPERTIES
    RUNTIME_OUTPUT_DIRECTORY ${CMAKE_BINARY_DIR}/bin
)
