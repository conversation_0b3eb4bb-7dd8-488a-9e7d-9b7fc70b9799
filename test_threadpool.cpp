#include "ThreadPool.h"
#include <iostream>
#include <chrono>
#include <random>
#include <vector>
#include <numeric>
#include <cassert>

// 测试用的简单计算函数
int fibonacci(int n)
{
    if (n <= 1)
        return n;
    return fibonacci(n - 1) + <PERSON><PERSON><PERSON><PERSON>(n - 2);
}

// 测试用的耗时任务
void heavy_task(int duration_ms)
{
    std::this_thread::sleep_for(std::chrono::milliseconds(duration_ms));
}

// 测试用的计算密集型任务
long long compute_sum(int start, int end)
{
    long long sum = 0;
    for (int i = start; i < end; ++i)
    {
        sum += i * i;
    }
    return sum;
}

void test_basic_functionality()
{
    std::cout << "=== 基本功能测试 ===" << std::endl;

    ThreadPool pool(4);
    std::vector<std::future<int>> results;

    // 提交一些简单任务
    for (int i = 0; i < 8; ++i)
    {
        results.emplace_back(
            pool.submit([i]
                        {
                std::cout << "Task " << i << " running on thread " 
                         << std::this_thread::get_id() << std::endl;
                return i * i; }));
    }

    // 获取结果
    for (int i = 0; i < 8; ++i)
    {
        int result = results[i].get();
        assert(result == i * i);
        std::cout << "Task " << i << " result: " << result << std::endl;
    }

    std::cout << "基本功能测试通过！" << std::endl
              << std::endl;
}

void test_performance()
{
    std::cout << "=== 性能测试 ===" << std::endl;

    const int num_tasks = 1000;
    const int num_threads = std::thread::hardware_concurrency();

    std::cout << "硬件并发线程数: " << num_threads << std::endl;
    std::cout << "测试任务数量: " << num_tasks << std::endl;

    // 测试线程池性能
    {
        ThreadPool pool(num_threads);
        auto start = std::chrono::high_resolution_clock::now();

        std::vector<std::future<long long>> results;
        for (int i = 0; i < num_tasks; ++i)
        {
            results.emplace_back(
                pool.submit(compute_sum, i * 1000, (i + 1) * 1000));
        }

        long long total_sum = 0;
        for (auto &result : results)
        {
            total_sum += result.get();
        }

        auto end = std::chrono::high_resolution_clock::now();
        auto duration = std::chrono::duration_cast<std::chrono::milliseconds>(end - start);

        std::cout << "线程池执行时间: " << duration.count() << " ms" << std::endl;
        std::cout << "总和: " << total_sum << std::endl;
    }

    // 测试单线程性能作为对比
    {
        auto start = std::chrono::high_resolution_clock::now();

        long long total_sum = 0;
        for (int i = 0; i < num_tasks; ++i)
        {
            total_sum += compute_sum(i * 1000, (i + 1) * 1000);
        }

        auto end = std::chrono::high_resolution_clock::now();
        auto duration = std::chrono::duration_cast<std::chrono::milliseconds>(end - start);

        std::cout << "单线程执行时间: " << duration.count() << " ms" << std::endl;
        std::cout << "总和: " << total_sum << std::endl;
    }

    std::cout << "性能测试完成！" << std::endl
              << std::endl;
}

void test_concurrent_access()
{
    std::cout << "=== 并发访问测试 ===" << std::endl;

    ThreadPool pool(8);
    const int num_tasks = 100;
    std::atomic<int> counter(0);

    std::vector<std::future<void>> results;

    // 提交大量并发任务
    for (int i = 0; i < num_tasks; ++i)
    {
        results.emplace_back(
            pool.submit([&counter]
                        {
                // 模拟一些工作
                std::this_thread::sleep_for(std::chrono::milliseconds(10));
                counter.fetch_add(1); }));
    }

    // 等待所有任务完成
    for (auto &result : results)
    {
        result.get();
    }

    assert(counter.load() == num_tasks);
    std::cout << "并发任务完成数量: " << counter.load() << std::endl;
    std::cout << "并发访问测试通过！" << std::endl
              << std::endl;
}

void test_exception_handling()
{
    std::cout << "=== 异常处理测试 ===" << std::endl;

    ThreadPool pool(2);

    // 提交一个会抛出异常的任务
    auto future = pool.submit([]
                              {
        throw std::runtime_error("测试异常");
        return 42; });

    try
    {
        future.get();
        assert(false); // 不应该到达这里
    }
    catch (const std::runtime_error &e)
    {
        std::cout << "成功捕获异常: " << e.what() << std::endl;
    }

    // 确保线程池仍然可以正常工作
    auto normal_future = pool.submit([]
                                     { return 100; });
    assert(normal_future.get() == 100);

    std::cout << "异常处理测试通过！" << std::endl
              << std::endl;
}

void test_stress()
{
    std::cout << "=== 压力测试 ===" << std::endl;

    ThreadPool pool(16);
    const int num_iterations = 10;
    const int tasks_per_iteration = 500;

    for (int iter = 0; iter < num_iterations; ++iter)
    {
        std::vector<std::future<int>> results;

        // 提交大量任务
        for (int i = 0; i < tasks_per_iteration; ++i)
        {
            results.emplace_back(
                pool.submit([i]
                            {
                    // 随机工作负载
                    std::random_device rd;
                    std::mt19937 gen(rd());
                    std::uniform_int_distribution<> dis(1, 10);
                    
                    std::this_thread::sleep_for(std::chrono::milliseconds(dis(gen)));
                    return i; }));
        }

        // 验证结果
        for (int i = 0; i < tasks_per_iteration; ++i)
        {
            assert(results[i].get() == i);
        }

        std::cout << "压力测试迭代 " << (iter + 1) << "/" << num_iterations
                  << " 完成，待处理任务: " << pool.pending_tasks() << std::endl;
    }

    std::cout << "压力测试通过！" << std::endl
              << std::endl;
}

int main()
{
    std::cout << "开始线程池测试..." << std::endl
              << std::endl;

    try
    {
        test_basic_functionality();
        test_performance();
        test_concurrent_access();
        test_exception_handling();
        test_stress();

        std::cout << "所有测试通过！线程池实现正确且高效。" << std::endl;
    }
    catch (const std::exception &e)
    {
        std::cerr << "测试失败: " << e.what() << std::endl;
        return 1;
    }

    return 0;
}
