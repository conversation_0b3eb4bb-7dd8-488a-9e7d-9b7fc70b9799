#pragma once

#include <vector>
#include <queue>
#include <memory>
#include <thread>
#include <mutex>
#include <condition_variable>
#include <future>
#include <functional>
#include <stdexcept>
#include <atomic>

class ThreadPool
{
public:
    // 构造函数，创建指定数量的工作线程
    explicit ThreadPool(size_t threads);

    // 析构函数，等待所有任务完成并关闭线程池
    ~ThreadPool();

    // 提交任务到线程池，返回future对象用于获取结果
    template <class F, class... Args>
    auto submit(F &&f, Args &&...args)
        -> std::future<typename std::result_of<F(Args...)>::type>;

    // 获取线程池中的线程数量
    size_t size() const;

    // 获取当前队列中等待执行的任务数量
    size_t pending_tasks() const;

    // 检查线程池是否已停止
    bool is_stopped() const;

private:
    // 工作线程向量
    std::vector<std::thread> workers;

    // 任务队列
    std::queue<std::function<void()>> tasks;

    // 同步原语
    mutable std::mutex queue_mutex;
    std::condition_variable condition;

    // 停止标志
    std::atomic<bool> stop;
};

// 模板方法的实现必须在头文件中
template <class F, class... Args>
auto ThreadPool::submit(F &&f, Args &&...args)
    -> std::future<typename std::result_of<F(Args...)>::type>
{

    using return_type = typename std::result_of<F(Args...)>::type;

    // 创建packaged_task来包装任务
    auto task = std::make_shared<std::packaged_task<return_type()>>(
        std::bind(std::forward<F>(f), std::forward<Args>(args)...));

    std::future<return_type> res = task->get_future();

    {
        std::unique_lock<std::mutex> lock(queue_mutex);

        // 如果线程池已停止，不接受新任务
        if (stop)
        {
            throw std::runtime_error("submit on stopped ThreadPool");
        }

        // 将任务包装成void()函数并加入队列
        tasks.emplace([task]()
                      { (*task)(); });
    }

    // 通知一个等待的线程
    condition.notify_one();
    return res;
}
